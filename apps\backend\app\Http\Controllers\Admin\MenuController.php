<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\MenuRequest;
use App\Http\Resources\Admin\MenuResource;
use App\Models\Menu;
use App\Services\MenuService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Symfony\Component\HttpFoundation\Response;

/**
 * @group 菜单管理
 *
 * 系统菜单管理接口
 */
class MenuController extends Controller
{
    public function __construct(
        private MenuService $menuService
    ) {}

    /**
     * 获取菜单列表
     *
     * 获取当前用户有权限访问的菜单扁平数组，前端自行构建树形结构
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();

        // 如果是管理员或者没有启用权限控制，返回所有菜单
        if ($user->hasRole('admin')) {
            $data = $this->menuService->getMenuList();
        } else {
            // 根据用户权限过滤菜单
            $data = $this->menuService->getMenuListByUser($user);
        }

        return response()->json($data);
    }

    /**
     * 获取菜单树
     *
     * 用于选择父级菜单
     */
    public function tree(): AnonymousResourceCollection
    {
        $menus = $this->menuService->getMenuTree();

        return MenuResource::collection($menus);
    }

    /**
     * 创建菜单
     */
    public function store(MenuRequest $request): MenuResource
    {
        $menu = $this->menuService->create($request->validated());

        return new MenuResource($menu);
    }

    /**
     * 更新菜单
     */
    public function update(MenuRequest $request, Menu $menu): MenuResource
    {
        $menu = $this->menuService->update($menu, $request->validated());

        return new MenuResource($menu);
    }

    /**
     * 删除菜单
     */
    public function destroy(Menu $menu): Response
    {
        $this->menuService->delete($menu);

        return response()->noContent();
    }
}
