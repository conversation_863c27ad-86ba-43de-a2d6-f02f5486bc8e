<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\RoleMenuPermissionAssignRequest;
use App\Http\Resources\Admin\MenuResource;
use App\Http\Resources\Admin\RoleResource;
use App\Models\Menu;
use App\Models\Role;
use App\Services\RoleService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group 角色菜单权限管理
 *
 * 管理角色的菜单权限分配
 */
class RoleMenuPermissionController extends Controller
{
    public function __construct(
        private RoleService $roleService
    ) {}

    /**
     * 获取角色的菜单权限
     *
     * @urlParam role integer required 角色ID. Example: 1
     */
    public function show(Role $role): JsonResponse
    {
        $permissions = $this->roleService->getRoleMenuPermissions($role);
        
        return response()->json([
            'role' => new RoleResource($role),
            'permissions' => $permissions
        ]);
    }

    /**
     * 获取所有菜单及权限（用于权限分配页面）
     */
    public function getMenusForAssignment(): JsonResponse
    {
        $menus = Menu::with('permissions')
            ->where('status', true)
            ->orderBy('parent_id', 'asc')
            ->orderBy('sort', 'desc')
            ->orderBy('id', 'desc')
            ->get();

        return response()->json([
            'menus' => MenuResource::collection($menus)
        ]);
    }

    /**
     * 为角色分配菜单权限
     *
     * @urlParam role integer required 角色ID. Example: 1
     *
     * @bodyParam permissions array required 权限数组. Example: [{"menu_id": 1, "menu_permission_id": null}, {"menu_id": 2, "menu_permission_id": 1}]
     * @bodyParam permissions.*.menu_id integer required 菜单ID. Example: 1
     * @bodyParam permissions.*.menu_permission_id integer nullable 菜单权限ID，为空表示只有菜单访问权限. Example: 1
     */
    public function assign(RoleMenuPermissionAssignRequest $request, Role $role): JsonResponse
    {
        $this->roleService->assignMenuPermissions($role, $request->permissions);
        
        return response()->json([
            'message' => '权限分配成功',
            'role' => new RoleResource($role->load('roleMenuPermissions'))
        ]);
    }

    /**
     * 同步角色的菜单权限（覆盖式）
     *
     * @urlParam role integer required 角色ID. Example: 1
     *
     * @bodyParam permissions array required 权限数组（将完全替换角色当前的权限）. Example: [{"menu_id": 1, "menu_permission_id": null}]
     * @bodyParam permissions.*.menu_id integer required 菜单ID. Example: 1
     * @bodyParam permissions.*.menu_permission_id integer nullable 菜单权限ID，为空表示只有菜单访问权限. Example: 1
     */
    public function sync(RoleMenuPermissionAssignRequest $request, Role $role): JsonResponse
    {
        $this->roleService->syncMenuPermissions($role, $request->permissions);
        
        return response()->json([
            'message' => '权限同步成功',
            'role' => new RoleResource($role->load('roleMenuPermissions'))
        ]);
    }

    /**
     * 移除角色的菜单权限
     *
     * @urlParam role integer required 角色ID. Example: 1
     *
     * @bodyParam permissions array required 要移除的权限数组. Example: [{"menu_id": 1, "menu_permission_id": 1}]
     * @bodyParam permissions.*.menu_id integer required 菜单ID. Example: 1
     * @bodyParam permissions.*.menu_permission_id integer nullable 菜单权限ID. Example: 1
     */
    public function remove(RoleMenuPermissionAssignRequest $request, Role $role): JsonResponse
    {
        $this->roleService->removeMenuPermissions($role, $request->permissions);
        
        return response()->json([
            'message' => '权限移除成功',
            'role' => new RoleResource($role->load('roleMenuPermissions'))
        ]);
    }
}
