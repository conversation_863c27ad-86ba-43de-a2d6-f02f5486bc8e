<?php

namespace App\Models;

use App\Traits\HasAttachments;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasAttachments;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'tenant_id',
        'username',
        'password',
        'email',
        'phone',
        'status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'password' => 'hashed',
        ];
    }

    /**
     * 获取用户的角色
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'user_roles')
            ->withTimestamps();
    }

    /**
     * 检查用户是否有指定角色
     */
    public function hasRole(string $roleName): bool
    {
        return $this->roles()->where('name', $roleName)->exists();
    }

    /**
     * 分配角色给用户
     */
    public function assignRole($roles): void
    {
        if (is_string($roles)) {
            $roles = [$roles];
        }

        $roleIds = Role::whereIn('name', $roles)->pluck('id')->toArray();
        $this->roles()->syncWithoutDetaching($roleIds);
    }

    /**
     * 移除用户的角色
     */
    public function removeRole($roles): void
    {
        if (is_string($roles)) {
            $roles = [$roles];
        }

        $roleIds = Role::whereIn('name', $roles)->pluck('id')->toArray();
        $this->roles()->detach($roleIds);
    }

    /**
     * 同步用户角色
     */
    public function syncRoles($roles): void
    {
        if (is_string($roles)) {
            $roles = [$roles];
        }

        $roleIds = Role::whereIn('name', $roles)->pluck('id')->toArray();
        $this->roles()->sync($roleIds);
    }

    /**
     * 获取用户通过角色拥有的所有菜单权限
     */
    public function getAllPermissions()
    {
        $roleIds = $this->roles()->pluck('id');

        return RoleMenuPermission::whereIn('role_id', $roleIds)
            ->with(['menu', 'menuPermission'])
            ->get();
    }

    /**
     * 获取用户有权限访问的菜单列表
     */
    public function getAccessibleMenus()
    {
        $roleIds = $this->roles()->pluck('id');

        return Menu::whereIn('id', function($query) use ($roleIds) {
            $query->select('menu_id')
                  ->from('role_menu_permissions')
                  ->whereIn('role_id', $roleIds);
        })->with('permissions')->get();
    }

    /**
     * 检查用户是否有指定菜单的访问权限
     */
    public function hasMenuAccess(int $menuId): bool
    {
        $roleIds = $this->roles()->pluck('id');

        return RoleMenuPermission::whereIn('role_id', $roleIds)
            ->where('menu_id', $menuId)
            ->exists();
    }

    /**
     * 检查用户是否有指定菜单的特定权限
     */
    public function hasMenuPermission(int $menuId, int $menuPermissionId): bool
    {
        $roleIds = $this->roles()->pluck('id');

        return RoleMenuPermission::whereIn('role_id', $roleIds)
            ->where('menu_id', $menuId)
            ->where('menu_permission_id', $menuPermissionId)
            ->exists();
    }

    /**
     * 检查用户是否有指定菜单的权限标识
     */
    public function hasMenuPermissionByMark(string $menuName, string $authMark): bool
    {
        $roleIds = $this->roles()->pluck('id');

        return RoleMenuPermission::whereIn('role_id', $roleIds)
            ->whereHas('menu', function($query) use ($menuName) {
                $query->where('name', $menuName);
            })
            ->whereHas('menuPermission', function($query) use ($authMark) {
                $query->where('auth_mark', $authMark);
            })
            ->exists();
    }
}
