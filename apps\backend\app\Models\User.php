<?php

namespace App\Models;

use App\Traits\HasAttachments;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasAttachments;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'tenant_id',
        'username',
        'password',
        'email',
        'phone',
        'status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'password' => 'hashed',
        ];
    }

    /**
     * 获取用户的角色
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'user_roles')
            ->withTimestamps();
    }

    /**
     * 检查用户是否有指定角色
     */
    public function hasRole(string $roleName): bool
    {
        return $this->roles()->where('name', $roleName)->exists();
    }

    /**
     * 分配角色给用户
     */
    public function assignRole($roles): void
    {
        if (is_string($roles)) {
            $roles = [$roles];
        }

        $roleIds = Role::whereIn('name', $roles)->pluck('id')->toArray();
        $this->roles()->syncWithoutDetaching($roleIds);
    }

    /**
     * 移除用户的角色
     */
    public function removeRole($roles): void
    {
        if (is_string($roles)) {
            $roles = [$roles];
        }

        $roleIds = Role::whereIn('name', $roles)->pluck('id')->toArray();
        $this->roles()->detach($roleIds);
    }

    /**
     * 同步用户角色
     */
    public function syncRoles($roles): void
    {
        if (is_string($roles)) {
            $roles = [$roles];
        }

        $roleIds = Role::whereIn('name', $roles)->pluck('id')->toArray();
        $this->roles()->sync($roleIds);
    }
}
