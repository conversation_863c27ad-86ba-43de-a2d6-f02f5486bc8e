<?php

namespace App\Services;

use App\Models\Role;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class RoleService
{
    /**
     * 获取角色分页列表
     */
    public function paginate(int $perPage = 20, ?string $search = null): LengthAwarePaginator
    {
        $query = Role::query();

        if ($search) {
            $query->where('name', 'like', "%{$search}%");
        }

        return $query->withCount('users')
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * 创建角色
     */
    public function create(array $data): Role
    {
        return DB::transaction(function () use ($data) {
            return Role::create($data);
        });
    }

    /**
     * 更新角色
     */
    public function update(Role $role, array $data): Role
    {
        return DB::transaction(function () use ($role, $data) {
            $role->update($data);

            return $role->fresh();
        });
    }

    /**
     * 删除角色
     */
    public function delete(Role $role): void
    {
        // 检查是否有用户使用此角色
        if ($role->users()->exists()) {
            throw ValidationException::withMessages([
                'role' => '该角色下还有用户，无法删除',
            ]);
        }

        DB::transaction(function () use ($role) {
            // 删除角色（由于暂时没有权限功能，直接删除角色）
            $role->delete();
        });
    }

    /**
     * 获取角色下的用户
     */
    public function getUsersByRole(Role $role, int $perPage = 20): LengthAwarePaginator
    {
        return $role->users()
            ->with(['attachments'])
            ->paginate($perPage);
    }
}
