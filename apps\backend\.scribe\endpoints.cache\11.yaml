## Autogenerated by Scribe. DO NOT MODIFY.

name: 菜单管理
description: |-

  系统菜单管理接口
endpoints:
  -
    httpMethods:
      - GET
    uri: api/admin/menus
    metadata:
      groupName: 菜单管理
      groupDescription: |-

        系统菜单管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 获取菜单列表
      description: 获取当前用户有权限访问的菜单扁平数组，前端自行构建树形结构
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/menus/tree
    metadata:
      groupName: 菜单管理
      groupDescription: |-

        系统菜单管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 获取菜单树
      description: 用于选择父级菜单
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/menus
    metadata:
      groupName: 菜单管理
      groupDescription: |-

        系统菜单管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 创建菜单
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      parent_id:
        name: parent_id
        description: 'The <code>id</code> of an existing record in the menus table.'
        required: false
        example: null
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      name:
        name: name
        description: 'Must not be greater than 50 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      path:
        name: path
        description: 'Must not be greater than 255 characters.'
        required: true
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      component:
        name: component
        description: 'Must not be greater than 255 characters.'
        required: false
        example: g
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      title:
        name: title
        description: 'Must not be greater than 100 characters.'
        required: true
        example: z
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      icon:
        name: icon
        description: 'Must not be greater than 50 characters.'
        required: false
        example: m
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      label:
        name: label
        description: 'Must not be greater than 100 characters.'
        required: false
        example: i
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      sort:
        name: sort
        description: ''
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      is_hide:
        name: is_hide
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      is_hide_tab:
        name: is_hide_tab
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      link:
        name: link
        description: 'Must not be greater than 255 characters.'
        required: false
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      is_iframe:
        name: is_iframe
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      keep_alive:
        name: keep_alive
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      is_first_level:
        name: is_first_level
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      fixed_tab:
        name: fixed_tab
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      active_path:
        name: active_path
        description: 'Must not be greater than 255 characters.'
        required: false
        example: g
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      is_full_page:
        name: is_full_page
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      show_badge:
        name: show_badge
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      show_text_badge:
        name: show_text_badge
        description: 'Must not be greater than 50 characters.'
        required: false
        example: z
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      status:
        name: status
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      permissions:
        name: permissions
        description: ''
        required: false
        example: null
        type: 'object[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'permissions[].title':
        name: 'permissions[].title'
        description: 'This field is required when <code>permissions</code> is present. Must not be greater than 50 characters.'
        required: false
        example: m
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'permissions[].auth_mark':
        name: 'permissions[].auth_mark'
        description: 'This field is required when <code>permissions</code> is present. Must not be greater than 100 characters.'
        required: false
        example: i
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'permissions[].sort':
        name: 'permissions[].sort'
        description: ''
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      name: b
      path: 'n'
      component: g
      title: z
      icon: m
      label: i
      sort: 16
      is_hide: false
      is_hide_tab: false
      link: 'n'
      is_iframe: true
      keep_alive: false
      is_first_level: true
      fixed_tab: true
      active_path: g
      is_full_page: true
      show_badge: false
      show_text_badge: z
      status: true
      permissions:
        -
          title: m
          auth_mark: i
          sort: 16
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/admin/menus/{menu_id}'
    metadata:
      groupName: 菜单管理
      groupDescription: |-

        系统菜单管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 更新菜单
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      menu_id:
        name: menu_id
        description: 'The ID of the menu.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      menu_id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      parent_id:
        name: parent_id
        description: 'The <code>id</code> of an existing record in the menus table.'
        required: false
        example: null
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      name:
        name: name
        description: 'Must not be greater than 50 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      path:
        name: path
        description: 'Must not be greater than 255 characters.'
        required: true
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      component:
        name: component
        description: 'Must not be greater than 255 characters.'
        required: false
        example: g
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      title:
        name: title
        description: 'Must not be greater than 100 characters.'
        required: true
        example: z
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      icon:
        name: icon
        description: 'Must not be greater than 50 characters.'
        required: false
        example: m
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      label:
        name: label
        description: 'Must not be greater than 100 characters.'
        required: false
        example: i
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      sort:
        name: sort
        description: ''
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      is_hide:
        name: is_hide
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      is_hide_tab:
        name: is_hide_tab
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      link:
        name: link
        description: 'Must not be greater than 255 characters.'
        required: false
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      is_iframe:
        name: is_iframe
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      keep_alive:
        name: keep_alive
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      is_first_level:
        name: is_first_level
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      fixed_tab:
        name: fixed_tab
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      active_path:
        name: active_path
        description: 'Must not be greater than 255 characters.'
        required: false
        example: g
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      is_full_page:
        name: is_full_page
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      show_badge:
        name: show_badge
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      show_text_badge:
        name: show_text_badge
        description: 'Must not be greater than 50 characters.'
        required: false
        example: z
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      status:
        name: status
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      permissions:
        name: permissions
        description: ''
        required: false
        example: null
        type: 'object[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'permissions[].title':
        name: 'permissions[].title'
        description: 'This field is required when <code>permissions</code> is present. Must not be greater than 50 characters.'
        required: false
        example: m
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'permissions[].auth_mark':
        name: 'permissions[].auth_mark'
        description: 'This field is required when <code>permissions</code> is present. Must not be greater than 100 characters.'
        required: false
        example: i
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'permissions[].sort':
        name: 'permissions[].sort'
        description: ''
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      name: b
      path: 'n'
      component: g
      title: z
      icon: m
      label: i
      sort: 16
      is_hide: false
      is_hide_tab: false
      link: 'n'
      is_iframe: false
      keep_alive: false
      is_first_level: true
      fixed_tab: true
      active_path: g
      is_full_page: true
      show_badge: true
      show_text_badge: z
      status: false
      permissions:
        -
          title: m
          auth_mark: i
          sort: 16
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/menus/{menu_id}'
    metadata:
      groupName: 菜单管理
      groupDescription: |-

        系统菜单管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 删除菜单
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      menu_id:
        name: menu_id
        description: 'The ID of the menu.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      menu_id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
