<?php

namespace App\Services;

use App\Models\Menu;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class MenuService
{
    /**
     * 获取菜单列表（返回扁平数组）
     * 参考 CategoryController 的实现方式
     */
    public function getMenuList(): array
    {
        // 获取扁平化的菜单数据，按照 parent_id 和 sort 排序
        $menus = Menu::with('permissions')
            ->where('status', true)
            ->orderBy('parent_id', 'asc')
            ->orderBy('sort', 'desc')  // 数字越大越靠前
            ->orderBy('id', 'desc')
            ->get();

        // 直接返回扁平数组，前端会自己构建树形结构
        return ['menuList' => $menus];
    }

    /**
     * 获取菜单树（用于选择父级菜单）
     */
    public function getMenuTree(): Collection
    {
        return Menu::select('id', 'parent_id', 'title', 'name', 'path')
            ->where('status', true)
            ->orderBy('parent_id', 'asc')
            ->orderBy('sort', 'desc')
            ->orderBy('id', 'desc')
            ->get();
    }

    /**
     * 创建菜单
     */
    public function create(array $data): Menu
    {
        return DB::transaction(function () use ($data) {
            // 提取权限数据
            $permissions = $data['permissions'] ?? [];
            unset($data['permissions']);

            // 创建菜单
            $menu = Menu::create($data);

            // 创建权限按钮
            foreach ($permissions as $permission) {
                $menu->permissions()->create($permission);
            }

            return $menu->fresh();
        });
    }

    /**
     * 更新菜单
     */
    public function update(Menu $menu, array $data): Menu
    {
        return DB::transaction(function () use ($menu, $data) {
            // 提取权限数据
            $permissions = $data['permissions'] ?? [];
            unset($data['permissions']);

            // 更新菜单
            $menu->update($data);

            // 更新权限按钮（先删除再创建）
            $menu->permissions()->delete();
            foreach ($permissions as $permission) {
                $menu->permissions()->create($permission);
            }

            return $menu->fresh();
        });
    }

    /**
     * 删除菜单
     */
    public function delete(Menu $menu): bool
    {
        return DB::transaction(function () use ($menu) {
            // 检查是否有子菜单
            $hasChildren = Menu::where('parent_id', $menu->id)->exists();
            if ($hasChildren) {
                throw new \Exception('该菜单下有子菜单，无法删除');
            }

            // 删除权限按钮
            $menu->permissions()->delete();

            // 删除菜单
            return $menu->delete();
        });
    }
}
